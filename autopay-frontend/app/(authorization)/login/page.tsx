'use client'

import OauthMethod from '@/app/(authorization)/common/components/OauthMethod'
import { useStore } from '@/app/(authorization)/common/stores/store'
import FormItemPasswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import emitter from '@/lib/utils/eventBus'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import Cookies from 'js-cookie'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

export default function Component() {
  const { setMessage } = useStore()
  const router = useRouter()

  const FormSchema = z.object({
    email: z.string().email({
      message: 'Email không hợp lệ',
    }),
    password: z.string().min(8, {
      message: 'Mật khẩu phải có ít nhất 8 ký tự',
    }),
    remember: z.boolean(),
  })

  type FormValues = z.infer<typeof FormSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
      remember: false,
    },
  })

  emitter.on('usePasswordGenerator', (password: string) => {
    form.setValue('password', password)
  })

  const { isPending, mutate } = useMutation({
    mutationFn: (data: FormValues) =>
      queryFetchHelper('/login', {
        method: 'POST',
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          remember: data.remember,
        }),
      }),
    onMutate: () => {
      setMessage('')
    },
    onSuccess: (data, variables) => {
      toast.success(data.message)

      // Save token to cookie with different expiration based on rememberMe
      if (data.data?.access_token) {
        const cookieOptions: any = {
          secure: true,
          sameSite: 'lax' as const,
        }

        // If remember me is checked, set expiration to 1 year, otherwise make it a session cookie
        if (variables.remember) {
          cookieOptions.expires = 365
        }
        // If remember is false, don't set expires property to make it a session cookie

        Cookies.set(process.env.NEXT_PUBLIC_APP_NAME + '.authorization', data.data.access_token, cookieOptions)
      }

      router.push('/')
    },
    onError: (error: ApiResponse) => {
      toast.error(error.message || 'Có lỗi xảy ra, vui lòng thử lại')

      // Handle validation errors from backend
      const errorData = error?.data as any
      if (errorData?.errors) {
        Object.keys(errorData.errors).forEach((field) => {
          if (field === 'email') {
            form.setError('email', {
              message: errorData.errors[field][0],
              type: 'server',
            })
          } else if (field === 'password') {
            form.setError('password', {
              message: errorData.errors[field][0],
              type: 'server',
            })
          }
        })
      } else {
        setMessage(error.message || 'Có lỗi xảy ra, vui lòng thử lại')
      }
    },
  })

  return (
    <div className="max-w-lg space-y-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit((data) => mutate(data as unknown as FormValues))}>
          <Card className="mx-4 md:mx-0 md:border-0 md:shadow-none">
            <CardHeader className="space-y-1">
              <CardTitle className="text-center text-2xl">Chào mừng trở lại</CardTitle>
              <CardDescription className="text-center">Hãy nhập thông tin đăng nhập để tiếp tục</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <OauthMethod mode="login" />
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background text-muted-foreground px-2">Hoặc tiếp tục với</span>
                </div>
              </div>
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="<EMAIL>"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItemPasswordGenerator<FormValues>
                    label="Mật khẩu"
                    field={field}
                    showGenerator={false}
                  />
                )}
              />

              <FormField
                control={form.control}
                name="remember"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Ghi nhớ đăng nhập</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button
                className="w-full gap-2"
                disabled={isPending}>
                {isPending && <Icons.spinner className="size-4 animate-spin" />}
                Đăng nhập
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
      <div className="px-6 text-center">
        Chưa có tài khoản?
        <Link
          href="/register"
          className="px-1 text-blue-500">
          Đăng ký
        </Link>
      </div>
    </div>
  )
}
