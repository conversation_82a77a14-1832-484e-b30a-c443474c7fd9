'use client'

import OauthMethod from '@/app/(authorization)/common/components/OauthMethod'
import { useStore } from '@/app/(authorization)/common/stores/store'
import FormItemPasswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import emitter from '@/lib/utils/eventBus'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { RocketIcon } from 'lucide-react'

import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

export default function Component() {
  const { message, setMessage } = useStore()

  const FormSchema = z.object({
    firstName: z.string().min(1, {
      message: 'Họ và tên đệm không được để trống',
    }),
    lastName: z.string().min(1, {
      message: 'Tên không được để trống',
    }),
    phone: z
      .string()
      .min(10, {
        message: 'Số điện thoại phải có ít nhất 10 số',
      })
      .regex(/^[0-9]+$/, {
        message: 'Số điện thoại chỉ được chứa số',
      }),
    email: z.string().email({
      message: 'Email không hợp lệ',
    }),
    password: z.string().min(8, {
      message: 'Mật khẩu phải có ít nhất 8 ký tự',
    }),
    agreeToTerms: z.boolean().refine((value) => value === true, {
      message: 'Bạn phải đồng ý với điều khoản dịch vụ và chính sách bảo mật',
    }),
  })

  type FormValues = z.infer<typeof FormSchema>

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      password: '',
      agreeToTerms: false,
    },
  })

  emitter.on('usePasswordGenerator', (password: string) => {
    form.setValue('password', password)
  })

  const { isPending, mutate } = useMutation({
    mutationFn: (data: FormValues) =>
      queryFetchHelper('/register', {
        method: 'POST',
        body: JSON.stringify({
          first_name: data.firstName,
          last_name: data.lastName,
          phone: data.phone,
          email: data.email,
          password: data.password,
        }),
      }),
    onMutate: () => {
      setMessage('')
    },
    onSuccess: (data) => {
      toast.success(data.message)
      setMessage(data.message)
    },
    onError: (error: ApiResponse) => {
      // Handle validation errors from backend
      const errorData = error?.data as any
      if (errorData?.errors) {
        Object.keys(errorData.errors).forEach((field) => {
          const fieldMap: Record<string, keyof FormValues> = {
            first_name: 'firstName',
            last_name: 'lastName',
            phone_: 'phone',
            email: 'email',
            password: 'password',
          }

          const formField = fieldMap[field]
          if (formField) {
            form.setError(formField, {
              message: errorData.errors[field][0],
              type: 'server',
            })
          }
        })
      } else {
        toast.error(error.message || 'Có lỗi xảy ra, vui lòng thử lại')
      }
    },
  })

  return (
    <>
      {message && (
        <div className="p-6">
          <Alert className="border-green-500">
            <RocketIcon className="h-4 w-4" />
            <AlertTitle>Chúc mừng!</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        </div>
      )}

      {!message && (
        <div className="max-w-lg space-y-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit((data) => mutate(data))}>
              <Card className="mx-4 md:mx-0 md:border-0 md:shadow-none">
                <CardHeader className="space-y-1">
                  <CardTitle className="text-center text-2xl">Tạo tài khoản</CardTitle>
                  <CardDescription className="text-center">Hãy nhập thông tin để đăng ký</CardDescription>
                </CardHeader>
                <CardContent className="grid gap-4">
                  <OauthMethod mode="register" />
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background text-muted-foreground px-2">Hoặc tiếp tục với</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>Họ và tên đệm</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Nguyễn Văn"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>Tên</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="An"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Số điện thoại</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="0123456789"
                            type="tel"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="<EMAIL>"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItemPasswordGenerator<FormValues>
                        label="Mật khẩu"
                        field={field}
                      />
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="agreeToTerms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="leading-none">
                          <FormLabel className="inline text-sm font-normal">
                            Tôi đồng ý với
                            <Link
                              href="/terms"
                              className="px-1 text-blue-500 hover:underline">
                              Điều khoản dịch vụ
                            </Link>
                            và
                            <Link
                              href="/privacy"
                              className="px-1 text-blue-500 hover:underline">
                              Chính sách bảo mật
                            </Link>
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full gap-2"
                    disabled={isPending}>
                    {isPending && <Icons.spinner className="size-4 animate-spin" />}
                    Tạo tài khoản
                  </Button>
                </CardFooter>
              </Card>
            </form>
          </Form>

          <div className="px-6 text-center">
            Đã có tài khoản?
            <Link
              href="/login"
              className="px-1 text-blue-500">
              Đăng nhập
            </Link>
          </div>
        </div>
      )}
    </>
  )
}
