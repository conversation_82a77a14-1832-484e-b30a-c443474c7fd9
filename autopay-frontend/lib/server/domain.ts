import { headers } from 'next/headers'
import type { DomainConfig } from '../types/domain'

/**
 * Get domain configuration on the server side
 */
export async function getDomainConfig(): Promise<DomainConfig | null> {
  try {
    const headersList = await headers()

    // Try to get domain config from middleware headers first
    const domainConfigHeader = headersList.get('x-domain-config')
    if (domainConfigHeader) {
      return JSON.parse(domainConfigHeader)
    }

    // Fallback: check if core hostname or fetch from API
    const hostname = headersList.get('host') || 'localhost'

    // Check if hostname matches core frontend URL
    const coreFrontendUrl = process.env.NEXT_PUBLIC_APP_URL
    const coreFrontendHost = coreFrontendUrl ? new URL(coreFrontendUrl).hostname : null

    if (hostname === coreFrontendHost) {
      // Return default core configuration
      return {
        id: 'core',
        frontend_hostname: coreFrontendHost,
        backend_hostname: process.env.NEXT_PUBLIC_API_URL
          ? new URL(process.env.NEXT_PUBLIC_API_URL).hostname
          : undefined,
        branding: {
          name: process.env.NEXT_PUBLIC_APP_NAME || 'Autopay',
          slogan: undefined,
          logo_url: undefined,
          favicon_url: undefined,
        },
        theme: {
          primary: '#3b82f6',
          secondary: '#64748b',
          accent: '#f59e0b',
          background: '#ffffff',
          surface: '#f8fafc',
          text: '#1e293b',
          text_secondary: '#64748b',
        },
        seo: {
          title: process.env.NEXT_PUBLIC_APP_NAME || 'Autopay',
          description: undefined,
          keywords: undefined,
          og_image: undefined,
        },
        contact: {},
        custom: {},
        custom_css: {},
      }
    }

    // Fetch from API for other hostnames
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/domains/config?hostname=${hostname}`, {
      headers: {
        Accept: 'application/json',
      },
      // Add cache control for server-side requests
      next: { revalidate: 300 }, // Cache for 5 minutes
    })

    if (!response.ok) {
      console.warn(`Failed to fetch domain config: ${response.statusText}`)
      return null
    }

    const data = await response.json()

    if (data.success) {
      return data.data
    } else {
      console.warn('Domain config fetch failed:', data.message)
      return null
    }
  } catch (error) {
    console.error('Error fetching domain config:', error)
    return null
  }
}

/**
 * Get hostname from headers
 */
export async function getHostname(): Promise<string> {
  const headersList = await headers()
  return headersList.get('host') || 'localhost'
}
