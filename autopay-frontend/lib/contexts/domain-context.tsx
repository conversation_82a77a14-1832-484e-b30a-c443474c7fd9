'use client'

import { useQuery } from '@tanstack/react-query'
import React, { createContext, useCallback, useEffect, useState } from 'react'
import { useMetaTags } from 'react-metatags-hook'
import type { DomainConfig, DomainContextType } from '../types/domain'
import { queryFetchHelper, resetApiUrl, setGlobalApiUrl } from '../utils/fetchHelper'

export const DomainContext = createContext<DomainContextType | null>(null)

interface DomainProviderProps {
  children: React.ReactNode
  initialConfig?: DomainConfig | null
}

export function DomainProvider({ children, initialConfig }: DomainProviderProps): React.JSX.Element {
  const [config, setConfig] = useState<DomainConfig | null>(initialConfig || null)

  // Use react-query to fetch domain config
  const hostname = typeof window !== 'undefined' ? window.location.hostname : ''
  const {
    data: domainData,
    isLoading,
    error: queryError,
    refetch,
  } = useQuery<ApiResponse<DomainConfig>>({
    queryKey: ['domainConfig', hostname],
    queryFn: () => queryFetchHelper(`/domains/config?hostname=${hostname}`),
    enabled: !initialConfig && !!hostname, // Only fetch if no initial config and hostname is available
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  const error = queryError?.message || null

  // Use meta tags hook for SEO
  useMetaTags(
    {
      title: config?.seo?.title || '',
      description: config?.seo?.description || '',
      metas: [...(config?.seo?.keywords ? [{ name: 'keywords', content: config.seo.keywords }] : [])],
      links: [...(config?.branding?.favicon_url ? [{ rel: 'icon', href: config.branding.favicon_url }] : [])],
      openGraph: {
        ...(config?.seo?.og_image ? { image: config.seo.og_image } : {}),
        ...(config?.seo?.title ? { title: config.seo.title } : {}),
        ...(config?.seo?.description ? { description: config.seo.description } : {}),
      },
    },
    [config?.seo, config?.branding?.favicon_url]
  )

  // Update config when data is fetched from react-query
  useEffect(() => {
    if (domainData?.success && domainData.data) {
      setConfig(domainData.data)
    }
  }, [domainData])

  // Update global API URL when config changes
  useEffect(() => {
    if (config?.backend_hostname) {
      setGlobalApiUrl(`https://${config.backend_hostname}`)
    } else {
      resetApiUrl()
    }
  }, [config?.backend_hostname])

  const refreshConfig = useCallback(async (): Promise<void> => {
    await refetch()
  }, [refetch])

  // Apply theme CSS variables when config changes
  useEffect(() => {
    if (config?.theme) {
      const root = document.documentElement

      // Apply theme colors as CSS custom properties
      Object.entries(config.theme).forEach(([key, value]) => {
        root.style.setProperty(`--domain-${key}`, value)
      })

      // Apply custom CSS
      if (config.custom_css && Object.keys(config.custom_css).length > 0) {
        const styleId = 'domain-custom-styles'
        let styleElement = document.getElementById(styleId) as HTMLStyleElement

        if (!styleElement) {
          styleElement = document.createElement('style')
          styleElement.id = styleId
          document.head.appendChild(styleElement)
        }

        // Convert custom CSS object to CSS string
        const cssString = Object.entries(config.custom_css)
          .map(([selector, styles]) => {
            if (typeof styles === 'object') {
              const styleProps = Object.entries(styles as Record<string, string>)
                .map(([prop, value]) => `${prop}: ${value};`)
                .join(' ')
              return `${selector} { ${styleProps} }`
            }
            return ''
          })
          .join('\n')

        styleElement.textContent = cssString
      }
    }
  }, [config])

  const contextValue: DomainContextType = {
    config,
    isLoading,
    error,
    refreshConfig,
  }

  return <DomainContext.Provider value={contextValue}>{children}</DomainContext.Provider>
}
